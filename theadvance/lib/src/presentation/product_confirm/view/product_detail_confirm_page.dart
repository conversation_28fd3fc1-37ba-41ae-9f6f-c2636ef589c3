// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/params/alert_params.dart';
import '../../../core/params/product_detail_confirm_request_params.dart';
import '../../../core/params/product_detail_confirm_update_params.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../domain/entities/entities.dart';
import '../../../injector/injector.dart';
import '../../widgets/widgets.dart';
import '../bloc/product_detail_confirm_bloc.dart';
import '../widgets/base_layout.dart';
import '../widgets/product_detail_confirm_body.dart';

@RoutePage()
class ProductDetailConfirmPage extends StatelessWidget {
  const ProductDetailConfirmPage({
    final Key? key,
    @PathParam('organizationId') required this.organizationId,
    @PathParam('id') required this.id,
  }) : super(key: key);
  final String organizationId;
  final String id;

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<ProductDetailConfirmBloc>()
        ..add(
          ProductDetailConfirmStarted(
            ProductDetailConfirmRequestParams(id, organizationId),
          ),
        ),
      child: BaseLayout(
        body: const ProductDetailConfirmBody(),
        title: Text(
          context.l10n.detail,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        bottomNavigationBar:
            BlocConsumer<ProductDetailConfirmBloc, ProductDetailConfirmState>(
              listener: (final context, final state) {
                if (state.status ==
                        ProductDetailConfirmStatus.approvalFailure ||
                    state.status == ProductDetailConfirmStatus.rejectFailure) {
                  final ApiError error = Utils.getData(state.data);
                  EzToast.showWidgetToast(
                    context,
                    body: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: SizedBox(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 12.0,
                          ),
                          child: Row(
                            spacing: 8,
                            children: [
                              DecoratedBox(
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: Icon(
                                    Icons.check,
                                    size: 24,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                                ),
                              ),
                              Text(
                                error.message ?? '',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
                if (state.status ==
                    ProductDetailConfirmStatus.approvalSuccess) {
                  context.router.popForced(true);
                  EzToast.showWidgetToast(
                    context,
                    body: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: .8),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: SizedBox(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 12.0,
                          ),
                          child: Row(
                            spacing: 8,
                            children: [
                              DecoratedBox(
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: Icon(
                                    Icons.check,
                                    size: 24,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                              Text(
                                context.l10n.requestApproved,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
                if (state.status == ProductDetailConfirmStatus.rejectSuccess) {
                  context.router.popForced(true);
                  EzToast.showWidgetToast(
                    context,
                    body: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: .8),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: SizedBox(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 12.0,
                          ),
                          child: Row(
                            spacing: 8,
                            children: [
                              DecoratedBox(
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: Icon(
                                    Icons.check,
                                    size: 24,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                              Text(
                                context.l10n.requestRejected,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
              },
              builder: (final context, final state) {
                if (state.status == ProductDetailConfirmStatus.success) {
                  final ProductDetailConfirm data = Utils.getData(state.data);
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(
                          color: Colors.grey.withValues(alpha: .3),
                        ),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                        top: 12,
                        left: 12.0,
                        right: 12,
                        bottom: 22,
                      ),
                      child: _buildBottomButton(context, data),
                    ),
                  );
                }
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(color: Colors.grey.withValues(alpha: .3)),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Row(
                      spacing: 8,
                      children: [
                        Expanded(
                          child: BaseAppButton(
                            enable: false,
                            color: Theme.of(context).colorScheme.error,
                            title: context.l10n.reject,
                            onPressed: () {},
                          ),
                        ),
                        Expanded(
                          child: BaseAppButton(
                            enable: false,
                            title: context.l10n.approval2,
                            onPressed: () {},
                            icon: const Padding(
                              padding: EdgeInsets.only(right: 8),
                              child: SizedBox.square(
                                dimension: 18,
                                child: CircularProgressIndicator.adaptive(),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
      ),
    );
  }

  Row _buildBottomButton(
    final BuildContext context,
    final ProductDetailConfirm data,
  ) {
    return Row(
      spacing: 12,
      children: [
        Expanded(
          child: BaseAppButton(
            titleColor: Colors.white,
            color: Theme.of(context).colorScheme.error,
            title: context.l10n.reject,
            onPressed: () {
              final TextEditingController controller = TextEditingController(
                text: '',
              );
              showCupertinoModalPopup(
                context: context,
                builder: (final context) {
                  return Padding(
                    padding: EdgeInsetsGeometry.only(
                      top: MediaQuery.of(context).viewPadding.top + 10,
                    ),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        gradient: gradientBg,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(
                            context,
                            title: context.l10n.reject,
                            actionWidget: const SizedBox(),
                            ctl: controller,
                          ),
                          Expanded(
                            child: SingleChildScrollView(
                              child: Padding(
                                padding: const EdgeInsets.all(18),
                                child: Column(
                                  spacing: 8,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16.0,
                                      ),
                                      child: Text(
                                        context.l10n.reason.toUpperCase(),
                                        style: styleTitle(context),
                                      ),
                                    ),
                                    Theme(
                                      data: ThemeData(
                                        inputDecorationTheme:
                                            InputDecorationTheme(
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                borderSide: BorderSide(
                                                  width: .3,
                                                  color: Theme.of(
                                                    context,
                                                  ).primaryColor,
                                                ),
                                              ),
                                            ),
                                      ),
                                      child: TextField(
                                        autofocus: true,
                                        controller: controller,
                                        maxLines: 15,
                                        keyboardType: TextInputType.multiline,
                                        decoration: InputDecoration(
                                          isDense: true,
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                horizontal: 16,
                                                vertical: 13,
                                              ),
                                          hintText: context.l10n.inputContent,
                                          filled: true,
                                          fillColor: Colors.white,
                                          hintStyle: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                color: Theme.of(
                                                  context,
                                                ).hintColor,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          DecoratedBox(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border(
                                top: BorderSide(
                                  color: Theme.of(
                                    context,
                                  ).hintColor.withValues(alpha: .35),
                                ),
                              ),
                            ),
                            child: ValueListenableBuilder(
                              valueListenable: controller,
                              builder:
                                  (final context, final vText, final child) {
                                    return Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: BaseAppButton(
                                        enable: vText.text.isNotEmpty,
                                        onPressed: () {
                                          Alert.showAlertConfirmV2(
                                            AlertConfirmParams(
                                              context,
                                              message: context
                                                  .l10n
                                                  .doYouRejectPurchase,
                                              onPressed: () {
                                                Navigator.of(context).pop();
                                                Navigator.of(context).pop(true);
                                              },
                                              onPressedCancel: () {
                                                Navigator.of(context).pop();
                                              },
                                              colorConfirmText: Colors.red,
                                              confirmText: context.l10n.reject,
                                              cancelButton: context.l10n.cancel,
                                            ),
                                          );
                                        },
                                        title: context.l10n.send,
                                      ),
                                    );
                                  },
                            ),
                          ),
                          AnimatedSize(
                            duration: const Duration(milliseconds: 850),
                            child: SizedBox(
                              height: MediaQuery.of(context).viewInsets.bottom,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ).then((final val) {
                if (val != null && val is bool) {
                  if (context.mounted) {
                    _onReject(context, data, controller.value);
                  }
                }
              });
            },
            icon: const Icon(Icons.close, size: 18, color: Colors.white),
          ),
        ),
        Expanded(
          child: BaseAppButton(
            titleColor: Colors.white,
            title: context.l10n.approval2,
            onPressed: () {
              Alert.showAlertConfirmV2(
                AlertConfirmParams(
                  title: context.l10n.newUpdate,
                  onPressedCancel: () {
                    Navigator.of(context).pop();
                  },
                  context,
                  message: context.l10n.doYouConfirmPurchase,
                  onPressed: () {
                    context.read<ProductDetailConfirmBloc>().add(
                      ProductDetailConfirmApproval(
                        ProductDetailConfirmUpdateParams(
                          rowKey: data.rowKey,
                          organizationId: data.organizationID,
                          employeeId: EZCache.shared
                              .getUserProfile()
                              ?.employeeId,
                          employeeName: EZCache.shared.getUserProfile()?.name,
                          note: data.content,
                        ),
                      ),
                    );
                    Navigator.of(context).pop();
                  },
                  confirmText: context.l10n.approval2,
                  cancelButton: context.l10n.cancel,
                  image: EZResources.image(
                    ImageParams(name: AppIcons.icApprovalSuccess),
                  ),
                  gradient: gradient_bg2,
                ),
              );
            },
            icon: EZResources.image(
              ImageParams(name: AppIcons.icCheckApprove, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  void _onReject(
    final BuildContext context,
    final ProductDetailConfirm data,
    final TextEditingValue vText,
  ) {
    return context.read<ProductDetailConfirmBloc>().add(
      ProductDetailConfirmReject(
        ProductDetailConfirmUpdateParams(
          rowKey: data.rowKey,
          organizationId: data.organizationID,
          employeeId: EZCache.shared.getUserProfile()?.employeeId,
          employeeName: EZCache.shared.getUserProfile()?.name,
          note: vText.text.trim(),
        ),
      ),
    );
  }

  Widget _buildHeader(
    final BuildContext context, {
    required final String title,
    required final Widget actionWidget,
    required final TextEditingController ctl,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: Stack(
        children: [
          ValueListenableBuilder(
            valueListenable: ctl,
            builder: (final context, final vContent, final child) {
              return Align(
                alignment: Alignment.centerLeft,
                child: IconButton(
                  onPressed: () {
                    if (vContent.text.isEmpty) {
                      Navigator.of(context).pop();
                    } else {
                      showCupertinoModalPopup(
                        barrierColor: Colors.black54,
                        context: context,
                        builder: (final context) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: CupertinoActionSheet(
                            cancelButton: CupertinoButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text(
                                context.l10n.resumeContent,
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                            actions: <Widget>[
                              SizedBox(
                                width: double.infinity,
                                height: 39,
                                child: ColoredBox(
                                  color: Colors.white,
                                  child: Center(
                                    child: Text(
                                      context.l10n.comfirmApprovalSuccess,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            fontSize: 13,
                                            color: Theme.of(context).hintColor,
                                          ),
                                    ),
                                  ),
                                ),
                              ),
                              CupertinoButton(
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(8),
                                  bottomRight: Radius.circular(8),
                                ),
                                color: Colors.white,
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  Navigator.of(context).pop();
                                },
                                child: Text(
                                  context.l10n.removeContent,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                  },
                  icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
                ),
              );
            },
          ),
          Center(
            child: Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontSize: 18),
            ),
          ),
          Align(alignment: Alignment.centerRight, child: actionWidget),
        ],
      ),
    );
  }
}
