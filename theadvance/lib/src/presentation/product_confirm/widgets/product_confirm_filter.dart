// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/params/request_params.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../domain/entities/product_confirm_branch.dart';
import '../../../injector/injector.dart';
import '../../settings/fonts/fonts_bloc.dart';
import '../../widgets/widgets.dart';
import '../product_confirm.dart';

class ProductConfirmFilter extends StatefulWidget {
  const ProductConfirmFilter({super.key, required this.organizationId});
  final String organizationId;

  static Future<void> showBottomFilter(
    final BuildContext context, {
    required final String organizationId,
  }) => showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (final context) => ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: ProductConfirmFilter(organizationId: organizationId),
    ),
  );

  @override
  State<ProductConfirmFilter> createState() => _ProductConfirmFilterState();
}

class _ProductConfirmFilterState extends State<ProductConfirmFilter> {
  List<DateTime> datetimes = [];
  late PageController pageController;
  final ValueNotifier<String> vTittleDate = ValueNotifier('');
  final ValueNotifier<int> iPage = ValueNotifier(0);
  final ValueNotifierList<ProductConfirmBranchItems> items = ValueNotifierList(
    [],
  );
  @override
  void initState() {
    pageController = PageController(initialPage: DateTime.now().month - 1);
    List.generate(24, (final i) {
      datetimes.add(DateTime(DateTime.now().year, i + 1));
    });
    setIndex(pageController.initialPage);
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(gradient: gradientBg),
      child: SizedBox(
        height: MediaQuery.sizeOf(context).height * .6,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ValueListenableBuilder(
                      valueListenable: ProductConfirmPage.filterTemplate,
                      builder: (final context, final vFilter, final child) {
                        return _buildHeader(
                          context,
                          title: context.l10n.filterBy,
                        );
                      },
                    ),
                    const SizedBox(height: 32),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: _buildFilterBody(context),
                    ),
                  ],
                ),
              ),
            ),
            DecoratedBox(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(
                    width: .35,
                    color: Theme.of(context).dividerColor,
                  ),
                ),
              ),
              child: ValueListenableBuilder(
                valueListenable: ProductConfirmPage.filterTemplate,
                builder: (final context, final vFilter, final child) {
                  final isValue =
                      !(vFilter.branchs.isEmpty && vFilter.fromDay == null);
                  return Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      spacing: 12,
                      children: [
                        Expanded(
                          child: BaseAppButton(
                            height: 48,
                            borderColor: Theme.of(context).primaryColor,
                            enable: isValue,
                            color: Colors.white,
                            onPressed: () {
                              ProductConfirmPage.filterTemplate.value =
                                  ProductFilter(branchs: []);
                              ProductConfirmPage.filter.value = ProductFilter(
                                branchs: [],
                              );
                            },
                            title: context.l10n.removeFilter,
                            titleColor: Theme.of(context).primaryColor,
                            icon: EZResources.image(
                              ImageParams(
                                name: AppIcons.icRemoveFilter,
                                color: isValue
                                    ? Theme.of(context).primaryColor
                                    : Theme.of(
                                        context,
                                      ).hintColor.withValues(alpha: .5),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: BaseAppButton(
                            height: 48,
                            titleColor: Colors.white,
                            enable: isValue,
                            onPressed: () {
                              ProductConfirmPage.filter.value =
                                  ProductConfirmPage.filterTemplate.value;
                              Navigator.of(context).pop();
                            },
                            title: context.l10n.confirm,
                            icon: EZResources.image(
                              ImageParams(
                                name: AppIcons.icCheckApprove,
                                color: isValue
                                    ? Colors.white
                                    : Theme.of(
                                        context,
                                      ).hintColor.withValues(alpha: .5),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(
    final BuildContext context, {
    required final String title,
    final Widget? actionWidget,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: IconButton(
              onPressed: () {
                Navigator.of(context).pop(true);
              },
              icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
            ),
          ),
          Center(
            child: Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontSize: 18),
            ),
          ),
          if (actionWidget != null)
            Align(alignment: Alignment.centerRight, child: actionWidget),
        ],
      ),
    );
  }

  Widget _buildFilterBody(final BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ProductConfirmPage.filterTemplate,
      builder: (final context, final vFilter, final child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () {
                _onShowBottomField(context, vFilter.branchs);
              },
              child: _buildFilterField(
                context,
                hintText: context.l10n.choiceOffice,
                pathIcon: AppIcons.icCaretDown,
                title: context.l10n.departmentRoom.toUpperCase(),
                value: '',
              ),
            ),
            if (vFilter.branchs.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Wrap(
                  children: [
                    ...List.generate(vFilter.branchs.length, (final i) {
                      final item = vFilter.branchs[i];
                      return Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Chip(
                          labelPadding: const EdgeInsets.symmetric(vertical: 2),
                          backgroundColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              color: Theme.of(context).primaryColor,
                            ),
                            borderRadius: BorderRadiusGeometry.circular(18),
                          ),
                          onDeleted: () {
                            vFilter.branchs.removeAt(i);
                            ProductConfirmPage.filterTemplate.value = vFilter
                                .copyWith(branchs: vFilter.branchs);
                          },
                          label: Text(
                            item.name ?? '',
                            style: const TextStyle(color: Colors.white),
                          ),
                          deleteIconColor: Colors.white,
                        ),
                      );
                    }),
                  ],
                ),
              ),
            const SizedBox(height: 12),
            GestureDetector(
              onTap: () {
                _onShowDatePicker(context).then((final date) {
                  if (date != null) {
                    ProductConfirmPage.filterTemplate.value = ProductConfirmPage
                        .filterTemplate
                        .value
                        .copyWith(
                          fromDay: IntlHelper.dateFormatter.parse(date.first),
                          toDay: IntlHelper.dateFormatter.parse(date.last),
                        );
                  }
                });
              },
              child: _buildFilterField(
                context,
                hintText: context.l10n.selectDate,
                title: context.l10n.toDay.toUpperCase(),
                pathIcon: AppIcons.icCalendar,
                value:
                    (vFilter.fromDay != null &&
                        vFilter.toDay != null &&
                        vFilter.fromDay == vFilter.toDay)
                    ? '${vFilter.fromDay?.formatDate2()}'
                    : vFilter.fromDay == null && vFilter.toDay == null
                    ? ''
                    : '${vFilter.fromDay?.formatDate2()}'
                          ' - '
                          '${vFilter.toDay?.formatDate2()}',
              ),
            ),
          ],
        );
      },
    );
  }

  Future<List<String>?> _onShowDatePicker(final BuildContext context) {
    return showDialog<List<String>?>(
      context: context,
      builder: (final builder) {
        if (ProductConfirmPage.filterTemplate.value.fromDay == null &&
            ProductConfirmPage.filterTemplate.value.toDay == null) {
          pageController = PageController(
            initialPage: DateTime.now().month - 1,
          );
          setIndex(pageController.initialPage);
        } else {
          final datee = ProductConfirmPage.filterTemplate.value;
          final datesRange = dateRange(
            datee.fromDay ?? DateTime.now(),
            datee.toDay ?? DateTime.now(),
          );
          valDates.setValue([...datesRange.map((final e) => e.formatDate2())]);
          pageController = PageController(
            initialPage: (datee.fromDay?.month ?? 0) - 1,
          );
          setIndex(pageController.initialPage);
        }
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 12,
                    ),
                    child: ValueListenableBuilder(
                      valueListenable: iPage,
                      builder: (final context, final vPage, final child) {
                        return Row(
                          children: [
                            IconButton(
                              onPressed: vPage == 0
                                  ? null
                                  : () {
                                      pageController
                                          .previousPage(
                                            duration: const Duration(
                                              milliseconds: 200,
                                            ),
                                            curve: Curves.fastOutSlowIn,
                                          )
                                          .whenComplete(() {
                                            setIndex(
                                              pageController.page?.toInt(),
                                            );
                                          });
                                    },
                              icon: EZResources.image(
                                ImageParams(
                                  name: AppIcons.icPreCalendar,
                                  size: const ImageSize.square(24),
                                ),
                              ),
                            ),
                            const Spacer(),
                            ValueListenableBuilder(
                              valueListenable: vTittleDate,
                              builder: (final context, final vs, final child) {
                                return Text(
                                  vs,
                                  style: Theme.of(context).textTheme.titleSmall
                                      ?.copyWith(fontSize: 18),
                                );
                              },
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: vPage == datetimes.length - 1
                                  ? null
                                  : () {
                                      pageController
                                          .nextPage(
                                            duration: const Duration(
                                              milliseconds: 200,
                                            ),
                                            curve: Curves.fastOutSlowIn,
                                          )
                                          .whenComplete(() {
                                            setIndex(
                                              pageController.page?.toInt(),
                                            );
                                          });
                                    },
                              icon: EZResources.image(
                                ImageParams(
                                  name: AppIcons.icNextCalendar,
                                  size: const ImageSize.square(24),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  ValueListenableBuilder(
                    valueListenable: valDates,
                    builder: (final context, final vDates, final child) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: ExpandablePageView(
                              controller: pageController,
                              children: [
                                ...List.generate(datetimes.length, (final i) {
                                  return _calendarByMonth(vDates, datetimes[i]);
                                }),
                              ],
                              onPage: (final page) {
                                setIndex(page);
                              },
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: BaseAppButton(
                              titleColor: Colors.white,
                              enable: vDates.isNotEmpty,
                              onPressed: () {
                                Navigator.of(context).pop(vDates);
                              },
                              title: context.l10n.done,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Column _buildFilterField(
    final BuildContext context, {
    required final String title,
    required final String hintText,
    required final String pathIcon,
    required final String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
          child: Text(title, style: styleTitle(context)),
        ),
        DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          child: SizedBox(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      value.isEmpty ? hintText : value,
                      style: value.isEmpty
                          ? TextStyle(
                              color: Theme.of(
                                context,
                              ).hintColor.withValues(alpha: .8),
                            )
                          : null,
                    ),
                  ),
                  EZResources.image(ImageParams(name: pathIcon)),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<List<_DepartmentResult>?> _onShowBottomField(
    final BuildContext context,
    final List<ProductConfirmBranchItems> branchsFiltered,
  ) {
    final ValueNotifierList<ProductConfirmBranchItems> valSelect =
        ValueNotifierList(branchsFiltered);
    final TextEditingController controller = TextEditingController(text: '');
    return showCupertinoSheet(
      context: context,
      pageBuilder: (final builder) {
        return CupertinoPageScaffold(
          child: BlocProvider(
            create: (final context) => getIt<ProductConfirmBloc>()
              ..add(
                ProductConfirmBranchStarted(
                  ProductConfirmRequestParams(widget.organizationId),
                ),
              ),
            child: Material(
              child: DecoratedBox(
                decoration: BoxDecoration(gradient: gradientBg),
                child: Column(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: valSelect,
                      builder: (final context, final vFilter, final child) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildHeader(
                              context,
                              title: context.l10n.departmentRoom,
                              actionWidget: vFilter.isNotEmpty
                                  ? Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: DecoratedBox(
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).primaryColor,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0,
                                            vertical: 2,
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.check,
                                                size: 18,
                                                color: Theme.of(
                                                  context,
                                                ).colorScheme.surface,
                                              ),
                                              const Text(' '),
                                              Text(
                                                vFilter.length.toString(),
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .titleSmall
                                                    ?.copyWith(
                                                      fontSize: 14,
                                                      color: Theme.of(
                                                        context,
                                                      ).colorScheme.surface,
                                                    ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    )
                                  : const SizedBox(),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Theme(
                                data: ThemeData(
                                  inputDecorationTheme: InputDecorationTheme(
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                  ),
                                ),
                                child: TextField(
                                  controller: controller,
                                  decoration: InputDecoration(
                                    suffixIconConstraints: const BoxConstraints(
                                      minWidth: 24,
                                      minHeight: 24,
                                    ),
                                    prefixIconConstraints: const BoxConstraints(
                                      minWidth: 24,
                                      minHeight: 24,
                                    ),
                                    prefixIcon: Padding(
                                      padding: const EdgeInsets.only(left: 12),
                                      child: EZResources.image(
                                        ImageParams(
                                          name: AppIcons.icSearch,
                                          color: Theme.of(context).hintColor,
                                        ),
                                      ),
                                    ),
                                    suffixIcon: Padding(
                                      padding: const EdgeInsets.only(right: 12),
                                      child: ValueListenableBuilder(
                                        valueListenable: controller,
                                        builder:
                                            (
                                              final context,
                                              final vText,
                                              final child,
                                            ) {
                                              return vText.text.isNotEmpty
                                                  ? GestureDetector(
                                                      onTap: () {
                                                        controller.clear();
                                                      },
                                                      child: Icon(
                                                        size: 24,
                                                        Icons.cancel,
                                                        color: Theme.of(context)
                                                            .hintColor
                                                            .withValues(
                                                              alpha: .5,
                                                            ),
                                                      ),
                                                    )
                                                  : const SizedBox();
                                            },
                                      ),
                                    ),
                                    contentPadding: const EdgeInsets.only(
                                      left: 12,
                                      top: 22,
                                      right: 22,
                                    ),
                                    hint: Text(
                                      context.l10n.search,
                                      style: TextStyle(
                                        color: Theme.of(context).hintColor,
                                      ),
                                    ),
                                    isDense: true,
                                    filled: true,
                                    fillColor: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    BlocConsumer<ProductConfirmBloc, ProductConfirmState>(
                      listener: (final context, final state) {
                        if (state.status ==
                            ProductConfirmStatus.getBranchsuccess) {
                          final ProductConfirmBranch data = Utils.getData(
                            state.data,
                          );
                          items.setValue(data.items);
                        }
                      },
                      builder: (final context, final state) {
                        return Expanded(
                          child: Stack(
                            children: [
                              _buildDepartmentBody(
                                context,
                                valSelect,
                                controller,
                              ),
                              AnimatedPositioned(
                                left: 0,
                                right: 0,
                                bottom: MediaQuery.of(
                                  context,
                                ).viewInsets.bottom,
                                duration: Duration.zero,
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border(
                                      top: BorderSide(
                                        width: .35,
                                        color: Theme.of(context).dividerColor,
                                      ),
                                    ),
                                  ),
                                
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      top: 12.0,
                                      left: 12,
                                      right: 12,
                                      bottom: 22,
                                    ),
                                    child: ValueListenableBuilder(
                                      valueListenable: valSelect,
                                      builder:
                                          (
                                            final context,
                                            final vSelected,
                                            final child,
                                          ) {
                                            return BaseAppButton(
                                              enable: vSelected.isNotEmpty,
                                              titleColor: Colors.white,
                                              onPressed: () {
                                                Navigator.of(context).pop(true);
                                              },
                                              title: context.l10n.done,
                                            );
                                          },
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    ).then((final val) {
      if (val != null && val) {
        ProductConfirmPage.filterTemplate.value = ProductConfirmPage
            .filterTemplate
            .value
            .copyWith(branchs: [...valSelect.value]);
      }
      return;
    });
  }

  AnimatedPositioned _buildDepartmentBody(
    final BuildContext context,
    final ValueNotifierList<ProductConfirmBranchItems> valSelect,
    final TextEditingController controller,
  ) {
    return AnimatedPositioned(
      left: 0,
      right: 0,
      top: 0,
      bottom: MediaQuery.of(context).viewInsets.bottom + 82,
      duration: Duration.zero,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: ValueListenableBuilder(
            valueListenable: valSelect,
            builder: (final context, final vSelected, final child) {
              return ValueListenableBuilder(
                valueListenable: items,
                builder: (final context, final vBranchs, final child) {
                  return ValueListenableBuilder(
                    valueListenable: controller,
                    builder: (final context, final vText, final child) {
                      final listSearched = vBranchs
                          .where(
                            (final e) =>
                                e.name?.toLowerCase().contains(
                                  vText.text.toLowerCase(),
                                ) ??
                                false,
                          )
                          .toList();
                      return ListView.separated(
                        itemCount: listSearched.length,
                        itemBuilder: (final context, final i) {
                          final item = listSearched[i];
                          return ListTile(
                            title: Text(item.name ?? ''),
                            trailing: _buildCheck(vSelected, item, context),
                            onTap: () {
                              final idx = vSelected.indexWhere(
                                (final e) => e.id == item.id,
                              );
                              if (idx != -1) {
                                valSelect.removeIndex(idx);
                              } else {
                                valSelect.add(item);
                              }
                            },
                          );
                        },
                        separatorBuilder: (final context, final i) =>
                            const Divider(),
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void setIndex(final int? i) {
    final datetime = datetimes[i ?? 0];
    vTittleDate.value = 'Tháng ${datetime.month}/${datetime.year}';
    iPage.value = i ?? 0;
  }

  Container _buildCheck(
    final List<ProductConfirmBranchItems> vSelected,
    final ProductConfirmBranchItems item,
    final BuildContext context,
  ) {
    return Container(
      width: 24,
      height: 24,
      decoration: vSelected.indexWhere((final e) => e.id == item.id) != -1
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Theme.of(context).primaryColor,
              border: Border.all(
                width: .35,
                color: Theme.of(context).primaryColor,
              ),
            )
          : BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(width: .35),
            ),
      child: vSelected.indexWhere((final e) => e.id == item.id) != -1
          ? Center(
              child: Icon(
                Icons.check,
                size: 18,
                color: Theme.of(context).colorScheme.surface,
              ),
            )
          : null,
    );
  }

  ValueNotifierList<String> valDates = ValueNotifierList([]);

  Widget _calendarByMonth(final List<String> dates, final DateTime time) {
    const rowHeight = 42.0;

    return BlocBuilder<FontsBloc, FontsState>(
      builder: (final context, final state) {
        return Column(
          children: [
            TableCalendar<dynamic>(
              daysOfWeekHeight: 24 * state.textScale,
              rowHeight: rowHeight * state.textScale,
              startingDayOfWeek: StartingDayOfWeek.monday,
              calendarBuilders: CalendarBuilders<dynamic>(
                dowBuilder: (final context, final day) {
                  final mapDay = {
                    DateTime.monday: 'Th2',
                    DateTime.tuesday: 'Th3',
                    DateTime.wednesday: 'Th4',
                    DateTime.thursday: 'Th5',
                    DateTime.friday: 'Th6',
                    DateTime.saturday: 'Th7',
                    DateTime.sunday: 'CN',
                  };
                  return Center(
                    child: Text(
                      mapDay[day.weekday] ?? '',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontSize: 14 * state.textScale,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).hintColor,
                      ),
                    ),
                  );
                },
                todayBuilder: (final context, final date, final focusedDay) {
                  final dateFormat2 = date.formatDate2();
                  return Padding(
                    padding: EdgeInsets.only(
                      left: (dates.isNotEmpty && dates.first == dateFormat2)
                          ? 12
                          : 0,
                      right:
                          (dates.isNotEmpty &&
                              dates.length > 1 &&
                              dates.last == dateFormat2)
                          ? 12
                          : 0,
                    ),
                    child: _buildDate(dates, dateFormat2, context, date),
                  );
                },
                defaultBuilder: (final context, final date, final events) {
                  final dateFormat2 = date.formatDate2();
                  return Padding(
                    padding: EdgeInsets.only(
                      left: (dates.isNotEmpty && dates.first == dateFormat2)
                          ? 12
                          : 0,
                      right:
                          (dates.isNotEmpty &&
                              dates.length > 1 &&
                              dates.last == dateFormat2)
                          ? 12
                          : 0,
                    ),
                    child: _buildDate(dates, dateFormat2, context, date),
                  );
                },
                disabledBuilder:
                    (final context, final date, final focusedDay) =>
                        const SizedBox(),
              ),
              onDaySelected: (final date, final _) {
                final dateFormat2 = date.formatDate2();
                if (dates.isNotEmpty && dates.length == 1) {
                  final datesRange = dateRange(
                    IntlHelper.dateFormatter.parse(dates.first),
                    date,
                  );
                  valDates.setValue([
                    ...datesRange.map((final e) => e.formatDate2()),
                  ]);
                } else if (dates.isEmpty) {
                  valDates.add(dateFormat2);
                } else {
                  valDates.setValue([]);
                  valDates.add(dateFormat2);
                }

                //   Navigator.of(context).pop(date);
              },
              focusedDay: time,
              headerVisible: false,
              locale: AppLocale.vi,
              firstDay: time,
              lastDay: time.copyWith(month: time.month + 1, day: 0),
            ),
          ],
        );
      },
    );
  }

  ClipRRect _buildDate(
    final List<String> dates,
    final String dateFormat2,
    final BuildContext context,
    final DateTime date,
  ) {
    return ClipRRect(
      borderRadius: (dates.isNotEmpty && dates.first == dateFormat2)
          ? BorderRadiusGeometry.only(
              topLeft: const Radius.circular(24),
              bottomLeft: const Radius.circular(24),
              topRight: Radius.circular(dates.length == 1 ? 24 : 0),
              bottomRight: Radius.circular(dates.length == 1 ? 24 : 0),
            )
          : (dates.isNotEmpty && dates.length > 1 && dates.last == dateFormat2)
          ? const BorderRadiusGeometry.only(
              topRight: Radius.circular(24),
              bottomRight: Radius.circular(24),
            )
          : BorderRadiusGeometry.zero,
      child: ColoredBox(
        color:
            (dates.isNotEmpty && dates.first == dateFormat2) ||
                (dates.isNotEmpty &&
                    dates.length > 1 &&
                    dates.last == dateFormat2)
            ? const Color(0xffDCFEC1)
            : Colors.white,
        child: ClipOval(
          clipBehavior:
              (dates.isNotEmpty && dates.first == dateFormat2) ||
                  (dates.isNotEmpty &&
                      dates.length > 1 &&
                      dates.last == dateFormat2)
              ? Clip.antiAlias
              : Clip.none,
          child: DecoratedBox(
            decoration: BoxDecoration(
              color:
                  (dates.isNotEmpty && dates.first == dateFormat2) ||
                      (dates.isNotEmpty &&
                          dates.length > 1 &&
                          dates.last == dateFormat2)
                  ? Theme.of(context).primaryColor
                  : checkDateContains(dates, dateFormat2)
                  ? const Color(0xffDCFEC1)
                  : Colors.white,
            ),
            child: Center(
              child: Text(
                date.day.toString(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color:
                      (dates.isNotEmpty && dates.first == dateFormat2) ||
                          (dates.isNotEmpty &&
                              dates.length > 1 &&
                              dates.last == dateFormat2)
                      ? Colors.white
                      : null,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool checkDateContains(final List<String> vDates, final String date) {
    return vDates.contains(date) && vDates.first != date;
  }

  List<DateTime> dateRange(final DateTime startDate, final DateTime endDate) {
    final List<DateTime> dates = [];
    DateTime current = DateTime(startDate.year, startDate.month, startDate.day);
    final DateTime end = DateTime(endDate.year, endDate.month, endDate.day);

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }
}

class _DepartmentResult {
  _DepartmentResult(this.id, this.name);

  final String id;
  final String name;
}
