// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/routes/app_router.dart';
import '../../../core/utils/mappers.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/ez_datasources.dart';
import '../../../data/models/combo_tag_model.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../injector/injector.dart';
import '../../collaborator/home/<USER>';
import '../../widgets/widgets.dart';
import '../bloc/tag_image_bloc.dart';

class TagImageBody extends StatefulWidget {
  const TagImageBody({
    super.key,
    required this.rooms,
    required this.tagsRecent,
  });
  final ValueNotifierList<CustomerGetRoomListItem?> rooms;
  final ValueNotifierList<ComboTag> tagsRecent;
  @override
  State<TagImageBody> createState() => _TagImageBodyState();
}

class _TagImageBodyState extends State<TagImageBody> {
  CustomerGetRoomList? data;
  List<String> histories = [];

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<TagImageBloc, TagImageState>(
      listener: (final context, final state) {
        if (state.status == TagImageStatus.success) {
          data = Utils.getData(state.data);
          widget.rooms.setValue(data?.items);
          // unawaited(
          //   context.router.push(
          //     ChatRoute(conversationId: data?.conversation?.id),
          //   ),
          // );
        }
      },
      builder: (final context, final state) {
        return Padding(
          padding: const EdgeInsets.only(left: 12, right: 12, top: 24),
          child: Column(
            spacing: 24,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildComponent(
                context.l10n.byRoom,
                body: ValueListenableBuilder(
                  valueListenable: widget.rooms,
                  builder: (final context, final vServices, final child) {
                    final buildServiceWidgets = vServices
                        .map(
                          (final e) => HomeMenuItem(
                            url: e?.iconUrl,
                            name: e?.roomName,
                            ontap: () {
                              context.router
                                  .push(
                                    TagByResultImageListRoute(
                                      tags: [
                                        ComboTag(
                                          itemGroupId: e?.roomCode,
                                          itemGroupName: e?.roomName,
                                        ).toJson(),
                                      ],
                                      rooms: vServices
                                          .map(
                                            (final e) =>
                                                e ?? CustomerGetRoomListItem(),
                                          )
                                          .toList(),
                                    ),
                                  )
                                  .whenComplete(() async {
                                    final history = await EZCache.shared
                                        .getHistoryRecent();
                                    histories = history;
                                    widget.tagsRecent.setValue(
                                      getIt<Mapper>().convertList(
                                        histories
                                            .map(
                                              (final e) =>
                                                  ComboTagModel.fromJson(
                                                    json.decode(e),
                                                  ),
                                            )
                                            .toList(),
                                      ),
                                    );
                                  });
                            },
                          ),
                        )
                        .toList();
                    return MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: GridView.count(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        crossAxisCount: 3,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        children: buildServiceWidgets,
                      ),
                    );
                  },
                ),
              ),
              Expanded(
                child: _buildComponent(
                  context.l10n.searchRecent,
                  body: Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 12, bottom: 32),
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: SizedBox(
                          width: double.infinity,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                            child: _buildTagRecent(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  ValueListenableBuilder<List<ComboTag>> _buildTagRecent() {
    return ValueListenableBuilder(
      valueListenable: widget.tagsRecent,
      builder: (final context, final vTagsRecent, final child) {
        return vTagsRecent.isEmpty
            ? Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    EZResources.image(
                      ImageParams(
                        name: AppImages.bgNotFoundGrey,
                        size: const ImageSize.fromWidth(130),
                      ),
                    ),
                    ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 180),
                      child: Text(
                        context.l10n.notFoundHistory,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 16,
                          color: Colors.black54,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              )
            : Wrap(
                runSpacing: 8,
                spacing: 8,
                children: List.generate(vTagsRecent.length, (final i) {
                  final comboTag = vTagsRecent[i];
                  return GestureDetector(
                    onTap: () async {
                      context.router
                          .push(
                            TagByResultImageListRoute(
                              tags: [comboTag.toJson()],
                              rooms: widget.rooms.value
                                  .map(
                                    (final e) => e ?? CustomerGetRoomListItem(),
                                  )
                                  .toList(),
                            ),
                          )
                          .whenComplete(() async {
                            final history = await EZCache.shared
                                .getHistoryRecent();
                            histories = history;
                            widget.tagsRecent.setValue(
                              getIt<Mapper>().convertList(
                                histories
                                    .map(
                                      (final e) => ComboTagModel.fromJson(
                                        json.decode(e),
                                      ),
                                    )
                                    .toList(),
                              ),
                            );
                          });
                    },
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          width: .35,
                          color: Theme.of(context).hintColor,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 12,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            EZResources.image(
                              ImageParams(
                                name: AppIcons.icSearch,
                                color: Colors.black45,
                                size: const ImageSize.square(18),
                              ),
                            ),
                            Text(
                              comboTag.tagName ?? '',
                              style: const TextStyle(color: Colors.black45),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              );
      },
    );
  }

  Widget _buildComponent(final String title, {required final Widget body}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        body,
      ],
    );
  }
}
