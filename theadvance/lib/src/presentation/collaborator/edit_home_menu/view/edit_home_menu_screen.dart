// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../injector/injector.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../../../widgets/widgets.dart';
import '../../home/<USER>';
import '../bloc/edit_home_menu_bloc.dart';
import '../widgets/edit_menu_item.dart';
import 'edit_home_menu_item.dart';
import 'edit_home_menu_target_item.dart';

@RoutePage()
class EditHomeMenuScreen extends StatelessWidget {
  const EditHomeMenuScreen({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) =>
          getIt<EditHomeMenuBloc>()..add(EditHomeMenuFetched()),
      child: const EditHomeMenuView(),
    );
  }
}

class EditHomeMenuView extends StatefulWidget {
  const EditHomeMenuView({final Key? key}) : super(key: key);

  @override
  EditHomeMenuViewState createState() => EditHomeMenuViewState();
}

class EditHomeMenuViewState extends State<EditHomeMenuView> {
  List<ServiceItemsModel?>? data;
  late Map<String, bool> checkListHomeMenu;
  final searchController = TextEditingController();
  List<ServiceItemsModel?> searchData = [];
  List<String?> favoriteCodeList = [];
  final GlobalKey _draggableKey = GlobalKey();
  final ValueNotifierList<ServiceItemsModel> dragMain = ValueNotifierList([
    ServiceItemsModel(),
    ServiceItemsModel(),
  ]);
  final ValueNotifierList<ServiceItemsModel> dragPin = ValueNotifierList([
    ServiceItemsModel(),
    ServiceItemsModel(),
    ServiceItemsModel(),
    ServiceItemsModel(),
  ]);
  // final ValueNotifierList<ServiceItemsModel>
  //dragMainInit = ValueNotifierList([
  //   ServiceItemsModel(),
  //   ServiceItemsModel(),
  // ]);
  // final ValueNotifierList<ServiceItemsModel>
  //dragPinInit = ValueNotifierList([
  //   ServiceItemsModel(),
  //   ServiceItemsModel(),
  //   ServiceItemsModel(),
  //   ServiceItemsModel(),
  // ]);
  @override
  void initState() {
    super.initState();
    checkListHomeMenu = {};
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      await _loadFavoriteServices();
    });
  }

  Future<void> _loadFavoriteServices() async {
    try {
      // final cachePin = await EZCache.shared.getHomeItem(
      //   CollaboratorKeys.pinSettingServices,
      // );
      // final cacheMain = await EZCache.shared.getHomeItem(
      //   CollaboratorKeys.mainSettingServices,
      // );
      final cacheShowPin = await EZCache.shared.getHomeItem(
        CollaboratorKeys.pinServices,
      );
      final cacheShowMain = await EZCache.shared.getHomeItem(
        CollaboratorKeys.mainServices,
      );
      if (cacheShowPin.isNotEmpty) {
        dragPin.setValue(
          cacheShowPin.map((final e) => e ?? ServiceItemsModel()).toList(),
        );
      }
      if (cacheShowMain.isNotEmpty) {
        dragMain.setValue(
          cacheShowMain.map((final e) => e ?? ServiceItemsModel()).toList(),
        );
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      title: Text(
        context.l10n.custom,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 18,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () async {
            for (var i = 0; i < dragPin.value.length; i++) {
              if (dragPin.value[i].code == null && i == 0) {
                dragPin.updateValuebyIndex(
                  i,
                  HomeBody.services.value.firstWhere(
                        (final e) => e?.code == 'SEARCH_CUSTOMER',
                      ) ??
                      ServiceItemsModel(),
                );
              }
              if (dragPin.value[i].code == null && i == 1) {
                dragPin.updateValuebyIndex(
                  i,
                  HomeBody.services.value.firstWhere(
                        (final e) => e?.code == 'WORKING',
                      ) ??
                      ServiceItemsModel(),
                );
              }
              if (dragPin.value[i].code == null && i == 2) {
                dragPin.updateValuebyIndex(
                  i,
                  HomeBody.services.value.firstWhere(
                        (final e) => e?.code == 'ADVISORY',
                      ) ??
                      ServiceItemsModel(),
                );
              }
              if (dragPin.value[i].code == null && i == 3) {
                dragPin.updateValuebyIndex(
                  i,
                  HomeBody.services.value.firstWhere(
                        (final e) => e?.code == 'CREATE_CUSTOMER',
                      ) ??
                      ServiceItemsModel(),
                );
              }
            }
            for (var i = 0; i < dragMain.value.length; i++) {
              if (dragMain.value[i].code == null && i == 0) {
                dragMain.updateValuebyIndex(
                  i,
                  HomeBody.services.value.firstWhere(
                        (final e) => e?.code == 'CHECK_IN',
                      ) ??
                      ServiceItemsModel(),
                );
              }
              if (dragMain.value[i].code == null && i == 1) {
                dragMain.updateValuebyIndex(
                  i,
                  HomeBody.services.value.firstWhere(
                        (final e) => e?.code == 'BOOKING_MEAL',
                      ) ??
                      ServiceItemsModel(),
                );
              }
            }
            EZCache.shared.saveHomeItem(
              CollaboratorKeys.mainServices,
              dragMain.value,
            );
            EZCache.shared.saveHomeItem(
              CollaboratorKeys.pinServices,
              dragPin.value,
            );
            EzToast.showShortToast(message: context.l10n.updateSuccess);

            if (context.mounted) {
              context.router.popForced();
            }
          },
          icon: Text(
            context.l10n.save,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 15,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ],
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          spacing: 32,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildComponent(
              context.l10n.homePin.toUpperCase(),
              body: Container(
                padding: const EdgeInsets.all(16),
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: ValueListenableBuilder(
                  valueListenable: dragPin,
                  builder: (final context, final vSelected, final child) {
                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ...List.generate(vSelected.length, (final iDrag) {
                          final item = vSelected[iDrag];
                          return Expanded(
                            child: item.url == null
                                ? EditHomeMenuTargetItem(
                                    dragTarget: dragPin,
                                    index: iDrag,
                                  )
                                : Stack(
                                    fit: StackFit.passthrough,
                                    children: [
                                      EditHomeMenuItem(service: item),
                                      Center(
                                        child: SizedBox(
                                          width: double.infinity,
                                          height: 70,
                                          child: Stack(
                                            children: [
                                              Positioned(
                                                top: -12,
                                                right: -10,
                                                child: IconButton(
                                                  onPressed: () {
                                                    dragPin.updateValuebyIndex(
                                                      iDrag,
                                                      ServiceItemsModel(),
                                                    );
                                                  },
                                                  icon: EZResources.image(
                                                    ImageParams(
                                                      name:
                                                          AppIcons.icCancelGrey,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                          );
                        }),
                      ],
                    );
                  },
                ),
              ),
            ),
            _buildComponent(
              context.l10n.homeMain.toUpperCase(),
              body: Container(
                padding: const EdgeInsets.all(16),
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: ValueListenableBuilder(
                  valueListenable: dragMain,
                  builder: (final context, final vSelected, final child) {
                    return Row(
                      children: [
                        ...List.generate(vSelected.length, (final iDrag) {
                          final item = vSelected[iDrag];
                          return Expanded(
                            child: item.url == null
                                ? EditHomeMenuTargetItem(
                                    dragTarget: dragMain,
                                    index: iDrag,
                                  )
                                : Stack(
                                    fit: StackFit.passthrough,
                                    children: [
                                      EditHomeMenuItem(service: item),
                                      Center(
                                        child: SizedBox(
                                          width: 110,
                                          height: 70,
                                          child: Stack(
                                            children: [
                                              Positioned(
                                                top: -10,
                                                right: 0,
                                                child: IconButton(
                                                  onPressed: () {
                                                    dragMain.updateValuebyIndex(
                                                      iDrag,
                                                      ServiceItemsModel(),
                                                    );
                                                  },
                                                  icon: EZResources.image(
                                                    ImageParams(
                                                      name:
                                                          AppIcons.icCancelGrey,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),

                                      // Positioned(
                                      //   top: -10,
                                      //   right: 5,
                                      //   child: IconButton(
                                      //     onPressed: () {
                                      //       dragMainInit.updateValuebyIndex(
                                      //         iDrag,
                                      //         ServiceItemsModel(),
                                      //       );
                                      //       dragMain.updateValuebyIndex(
                                      //         iDrag,
                                      //         ServiceItemsModel(),
                                      //       );
                                      //     },
                                      //     icon: const Icon(Icons.cancel),
                                      //   ),
                                      // ),
                                    ],
                                  ),
                          );
                        }),
                      ],
                    );
                  },
                ),
              ),
            ),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      context.l10n.featureList.toUpperCase(),
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Column(children: [_buildItemsBody()]),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComponent(final String title, {required final Widget body}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(height: 12),
        body,
      ],
    );
  }

  Expanded _buildItemsBody() {
    return Expanded(
      child: ValueListenableBuilder(
        valueListenable: dragMain,
        builder: (final context, final vMain, final chilf) {
          return ValueListenableBuilder(
            valueListenable: dragPin,
            builder: (final context, final vPin, final child) {
              final List<ServiceItemsModel> allServices = [...vMain, ...vPin];
              return ValueListenableBuilder(
                valueListenable: HomeBody.services,
                builder: (final context, final vServices, final child) {
                  final buildServiceWidgets = vServices
                      .map(
                        (final e) => Stack(
                          children: [
                            Positioned.fill(
                              child: LongPressDraggable<ServiceItemsModel>(
                                data: e,
                                dragAnchorStrategy: pointerDragAnchorStrategy,
                                feedback: _DraggingListItem(
                                  dragKey: _draggableKey,
                                  child: EditHomeMenuItem(service: e),
                                ),
                                child: Center(
                                  child: EditHomeMenuItem(service: e),
                                ),
                              ),
                            ),
                            if (allServices.indexWhere(
                                  (final item) => e?.code == item.code,
                                ) !=
                                -1)
                              Positioned.fill(
                                child: ColoredBox(
                                  color: Colors.white.withValues(alpha: .65),
                                ),
                              ),
                          ],
                        ),
                      )
                      .toList();
                  return GridView.count(
                    padding: EdgeInsets.zero,
                    crossAxisCount: 4,
                    shrinkWrap: true,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 12,
                    childAspectRatio: .8,
                    children: buildServiceWidgets,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget buildFavoriteMenu(
    final BuildContext context, {
    required final List<ServiceItemsModel?> data,
  }) {
    return ValueListenableBuilder(
      valueListenable: dragPin,
      builder: (final context, final vSelected, final child) {
        return Row(
          children: [
            for (final ServiceItemsModel item in vSelected)
              Expanded(
                child: item.url == null
                    ? const SizedBox.square(
                        dimension: 45,
                        child: ColoredBox(color: Colors.white),
                      )
                    : EditHomeMenuItem(service: item),
              ),
          ],
        );
      },
    );
  }

  ListView buildMenu(
    final BuildContext context, {
    required final List<ServiceItemsModel?> data,
  }) {
    return ListView.separated(
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (final _, final index) => EditMenuItem(
        image: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: EzCachedNetworkImage(
            imageUrl: data[index]?.url ?? '',
            width: 40,
            height: 40,
          ),
        ),
        title: data[index]?.name ?? '',
        button: IconButton(
          onPressed: () async {
            if (favoriteCodeList.contains(data[index]?.code)) {
              return;
            }
            favoriteCodeList.add(data[index]?.code);
            setState(() {});
          },
          icon: EZResources.image(
            ImageParams(
              name: AppIcons.icAddCircle,
              size: const ImageSize(24, 24),
            ),
          ),
        ),
      ),
      separatorBuilder: (final context, final index) => Container(
        height: 0.35,
        margin: const EdgeInsets.symmetric(vertical: 4),
        color: const Color(0xFFC7C7C7),
      ),
      itemCount: data.length,
    );
  }
}

class _DraggingListItem extends StatelessWidget {
  const _DraggingListItem({required this.dragKey, required this.child});

  final GlobalKey dragKey;
  final Widget child;

  @override
  Widget build(final BuildContext context) {
    return FractionalTranslation(
      translation: const Offset(-0.5, -0.2),
      child: ClipRRect(
        key: dragKey,
        borderRadius: BorderRadius.circular(12),
        child: SizedBox(
          height: 300,
          width: 300,
          child: Opacity(opacity: 0.86, child: child),
        ),
      ),
    );
  }
}
