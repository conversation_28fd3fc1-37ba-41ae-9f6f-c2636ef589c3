// Flutter imports:
// ignore_for_file: use_build_context_synchronously

// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:animations/animations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../core/nd_constants/nd_constant.dart';
import '../../../../core/params/home_work_leader_check_request_params.dart';
import '../../../../core/routes/app_router.dart';
import '../../../../core/routes/routes.dart';
import '../../../../core/utils/api_error_dialog.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../domain/entities/customer_get_room_list.dart';
import '../../../widgets/widgets.dart';
import '../../tabbar/tabbar.dart' as tabbar_screen;
import '../home.dart';
import 'home_menu_item_favorite.dart';
import 'home_snake.dart';

class HomeBody extends StatefulWidget {
  const HomeBody({
    final Key? key,
    required this.favoriteCodeList,
    required this.mainCodeList,
  }) : super(key: key);
  final ValueNotifierList<ServiceItemsModel?> favoriteCodeList;
  final ValueNotifierList<ServiceItemsModel?> mainCodeList;
  static ValueNotifierList<ServiceItemsModel?> services = ValueNotifierList([]);
  @override
  HomeBodyState createState() => HomeBodyState();
}

class HomeBodyState extends State<HomeBody> {
  int? totalNoti;
  @override
  void initState() {
    super.initState();
  }

  Future<void> _loadFavoriteServices() async {
    try {
      final cachePin = await EZCache.shared.getHomeItem(
        CollaboratorKeys.pinServices,
      );
      final cacheMain = await EZCache.shared.getHomeItem(
        CollaboratorKeys.mainServices,
      );

      if (cachePin.isEmpty) {
        widget.favoriteCodeList.setValue([
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'SEARCH_CUSTOMER',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'WORKING',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'ADVISORY',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'CREATE_CUSTOMER',
              ) ??
              ServiceItemsModel(),
        ]);
        EZCache.shared.saveHomeItem(
          CollaboratorKeys.pinServices,
          widget.favoriteCodeList.value,
        );
      }
      if (cacheMain.isEmpty) {
        widget.mainCodeList.setValue([
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'CHECK_IN',
              ) ??
              ServiceItemsModel(),
          HomeBody.services.value.firstWhere(
                (final e) => e?.code == 'BOOKING_MEAL',
              ) ??
              ServiceItemsModel(),
        ]);
        EZCache.shared.saveHomeItem(
          CollaboratorKeys.mainServices,
          widget.mainCodeList.value,
        );
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(final BuildContext context) {
    return Stack(
      children: [
        ValueListenableBuilder(
          valueListenable: widget.favoriteCodeList,
          builder: (final context, final vFavoriteCodeList, final child) {
            return Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        // spacing: 12,
                        children: [
                          if (vFavoriteCodeList.isNotEmpty) ...[
                            ValueListenableBuilder(
                              valueListenable: widget.favoriteCodeList,
                              builder:
                                  (
                                    final context,
                                    final vPinCodeList,
                                    final child,
                                  ) {
                                    return Padding(
                                      padding: const EdgeInsets.only(top: 24),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: buildFavoriteServices(
                                          vPinCodeList,
                                        ),
                                      ),
                                    );
                                  },
                            ),
                            const SizedBox(height: 12),
                          ],
                          ValueListenableBuilder(
                            valueListenable: widget.mainCodeList,
                            builder:
                                (
                                  final context,
                                  final vMainCodeList,
                                  final child,
                                ) {
                                  return Row(
                                    spacing: 16,
                                    children: vMainCodeList
                                        .map(
                                          (final e) => Expanded(
                                            child: SnakeBorderAnimation(
                                              service: e,
                                              title: e?.name,
                                              background:
                                                  EZResources.assetImage(
                                                    AppImages.bgButtonHome,
                                                  ),
                                              color: Theme.of(
                                                context,
                                              ).primaryColor,
                                              icon: EzCachedNetworkImage(
                                                imageUrl: e?.url,
                                                fit: BoxFit.contain,
                                                width: _itemWidth(context),
                                                height: _itemWidth(context),
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                  );
                                },
                          ),
                          const SizedBox(height: 12),
                          BlocConsumer<HomeBloc, HomeState>(
                            listener: (final context, final state) {
                              if (state is HomeFetchSuccess) {
                                _updateTabBarNotification(
                                  state.services?.tabBarData,
                                );
                                totalNoti = state.services?.notificationCount;
                                HomeBody.services.setValue(
                                  state.services?.menuItems ?? [],
                                );
                                _loadFavoriteServices();
                              }
                              if (state is HomeFetchFailure) {
                                unawaited(
                                  ApiErrorDialog.show(
                                    ApiErrorParams(context, state.apiError),
                                  ),
                                );
                              }
                              if (state is HomeWorkLeaderCheckSuccess) {
                                if (state.data?.isLeader ?? false) {
                                  unawaited(
                                    _showWorkOption(
                                      context,
                                      roomCode: state.roomCode,
                                    ),
                                  );
                                } else {
                                  unawaited(
                                    context.router.push(
                                      PxTaskListRoute(roomCode: state.roomCode),
                                    ),
                                  );
                                }
                              }
                              if (state is HomeConsultationLeaderCheckSuccess) {
                                if (state.data?.isLeader ?? false) {
                                  unawaited(
                                    _showConsultationManagerOption(context),
                                  );
                                } else {
                                  unawaited(
                                    context.router.push(
                                      ConsultationManagerRoute(),
                                    ),
                                  );
                                }
                              }
                              if (state is HomeCheckLeaderSuccess) {
                                final isLeader =
                                    state.homeCheckLeader?.isLeader ?? false;
                                if (!isLeader) {
                                  unawaited(
                                    context.router.push(
                                      CustomerScheduleRoute(isLeader: isLeader),
                                    ),
                                  );
                                } else {
                                  unawaited(
                                    _showAppointmentOption(
                                      context,
                                      isLeader: isLeader,
                                    ),
                                  );
                                }
                              }
                              if (state is HomeGetRoomListSuccess) {
                                final roomList = state.data?.items ?? [];
                                unawaited(_showSelectRoomBottomSheet(roomList));
                              }
                            },
                            builder: (final context, final state) {
                              return _buildHomeService();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  ValueListenableBuilder<List<ServiceItemsModel?>> _buildHomeService() {
    return ValueListenableBuilder(
      valueListenable: HomeBody.services,
      builder: (final context, final vServices, final child) {
        final buildServiceWidgets = vServices
            .map(
              (final e) => HomeMenuItem(service: e, url: e?.url, name: e?.name),
            )
            .toList();
        return GridView.count(
          padding: const EdgeInsets.symmetric(vertical: 12),
          crossAxisCount: 3,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: buildServiceWidgets,
        );
      },
    );
  }

  List<Widget> buildFavoriteServices(
    final List<ServiceItemsModel?> favoriteCodeList,
  ) {
    final dd = favoriteCodeList.map((final e) => e).toList();

    return dd
        .map((final e) => Expanded(child: HomeMenuFavoriteItem(service: e)))
        .toList();
  }

  double _itemWidth(final BuildContext context) {
    return MediaQuery.sizeOf(context).width / 8.5;
  }

  void _updateTabBarNotification(final TabbarModel? notifications) {
    tabbar_screen.MainBody.workNotification.value = _handleNullString(
      notifications?.work,
    );
    tabbar_screen.MainBody.historyCheckinNotification.value = _handleNullString(
      notifications?.checkin,
    );
    tabbar_screen.MainBody.moreNotification.value = _handleNullString(
      notifications?.more,
    );
  }

  String _handleNullString(final String? value) {
    return Utils.defaultOnEmpty(value, fallback: Strings.zero);
  }

  Future<void> _showAppointmentOption(
    final BuildContext context, {
    required final bool isLeader,
  }) {
    return showModal<void>(
      useRootNavigator: false,
      configuration: const FadeScaleTransitionConfiguration(
        transitionDuration: Duration(milliseconds: 500),
        reverseTransitionDuration: Duration(milliseconds: 200),
      ),
      context: context,
      builder: (final context) {
        return Center(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(40),
            child: Material(
              color: Theme.of(context).colorScheme.surface.withValues(alpha: 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (isLeader)
                    _buildAssignTaskOption(
                      context,
                      label: context.l10n.assignTask,
                      onTap: () async {
                        context.router.popForced();
                        await context.router.pushNamed(Routes.assignTask);
                      },
                    ),
                  const SizedBox(height: 16),
                  _buildTaskOption(
                    context,
                    label: context.l10n.bookingSchedule,
                    onTap: () async {
                      context.router.popForced();
                      await context.router.push(
                        CustomerScheduleRoute(isLeader: isLeader),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _showWorkOption(
    final BuildContext context, {
    required final String? roomCode,
  }) {
    return showModal<void>(
      useRootNavigator: false,
      configuration: const FadeScaleTransitionConfiguration(
        transitionDuration: Duration(milliseconds: 500),
        reverseTransitionDuration: Duration(milliseconds: 200),
      ),
      context: context,
      builder: (final context) {
        return Center(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(40),
            child: Material(
              color: Theme.of(context).colorScheme.surface.withValues(alpha: 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  _buildAssignTaskOption(
                    context,
                    label: context.l10n.assignWork,
                    onTap: () async {
                      await context.router.push(
                        PxListRoute(
                          lsLevel1Id: EZCache.shared.getUserProfile()?.branchId,
                          roomCode: roomCode,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTaskOption(
                    context,
                    label: context.l10n.taskList,
                    onTap: () async {
                      await context.router.push(
                        PxTaskListRoute(roomCode: roomCode),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _showConsultationManagerOption(final BuildContext context) {
    return showModal<void>(
      useRootNavigator: false,
      configuration: const FadeScaleTransitionConfiguration(
        transitionDuration: Duration(milliseconds: 500),
        reverseTransitionDuration: Duration(milliseconds: 200),
      ),
      context: context,
      builder: (final context) {
        return Center(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(40),
            child: Material(
              color: Theme.of(context).colorScheme.surface.withValues(alpha: 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  _buildAssignTaskOption(
                    context,
                    label: context.l10n.assignWork,
                    onTap: () => context.router.push(
                      ConsultationManagerRoute(isManager: true),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildTaskOption(
                    context,
                    label: context.l10n.taskList,
                    onTap: () =>
                        context.router.push(ConsultationManagerRoute()),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAssignTaskOption(
    final BuildContext context, {
    required final String label,
    required final void Function() onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: const LinearGradient(
            colors: [Color(0xff1101A2), Color(0xff1D01C8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Center(
              child: EZResources.image(
                ImageParams(
                  name: AppImages.assignTask,
                  size: const ImageSize.square(56),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: Theme.of(context).colorScheme.surface,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward,
              color: Theme.of(context).colorScheme.surface,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskOption(
    final BuildContext context, {
    required final String label,
    required final void Function() onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Theme.of(context).colorScheme.secondary,
        ),
        child: Row(
          children: [
            Center(
              child: EZResources.image(
                ImageParams(
                  name: AppImages.appointment,
                  size: const ImageSize.square(56),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: AutoSizeText(
                label,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: Theme.of(context).colorScheme.surface,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Icon(
              Icons.arrow_forward,
              color: Theme.of(context).colorScheme.surface,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectRoomBottomSheet(
    final List<CustomerGetRoomListItem?> roomList,
  ) {
    return showBaseBottomSheet(
      context: context,
      title: context.l10n.pickRoom,
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        width: double.maxFinite,
        padding: const EdgeInsets.only(bottom: 32),
        child: ListView.separated(
          itemCount: roomList.length,
          itemBuilder: (final BuildContext _, final int index) {
            return InkWell(
              onTap: () {
                Navigator.of(context).pop();
                context.read<HomeBloc>().add(
                  HomeWorkLeaderChecked(
                    HomeWorkLeaderCheckRequestParams(
                      roomCode: roomList[index]?.roomCode,
                    ),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  Utils.defaultOnEmpty(roomList[index]?.roomName),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          },
          separatorBuilder: (final BuildContext context, final int index) {
            return const Divider();
          },
        ),
      ),
    );
  }
}
