// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../core/routes/routes.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../domain/entities/hr_organization.dart';
import '../../widgets/widgets.dart';
import '../bloc/hr_organization_bloc.dart';
import '../view/hr_organization_page.dart';

class HrOrganizationBody extends StatefulWidget {
  const HrOrganizationBody({final Key? key, required this.toNextPage})
    : super(key: key);
  final String toNextPage;
  @override
  State<HrOrganizationBody> createState() => _HrOrganizationBodyState();
}

class _HrOrganizationBodyState extends State<HrOrganizationBody> {
  HrOrganization? data;
  int safeList(final HrOrganization? data) {
    if (data != null) {
      if (data.items.isNotEmpty) {
        return data.items.length;
      }
    }
    return 0;
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<HrOrganizationBloc, HrOrganizationState>(
      listener: (final context, final state) {
        if (state.status == HrOrganizationStatus.success) {
          data = Utils.getData(state.data);
          if (safeList(data) < 2) {
            if (widget.toNextPage == ToNextPage.HR.name) {
              unawaited(
                context.router.replaceNamed(
                  '${Routes.staffEvaluationPeriods}/${data?.items.first?.organizationId}',
                ),
              );
            }
            if (widget.toNextPage == ToNextPage.PRODUCT_CONFIRM.name) {
              unawaited(
                context.router.replaceNamed(
                  '${Routes.productConfirm}/${data?.items.first?.organizationId}',
                ),
              );
            }
            // if (widget.toNextPage == ToNextPage.INVENTORY.name) {
            //   SchedulerBinding.instance.addPostFrameCallback((
            //     final time,
            //   ) async {
            //     final response = await Api.postLogin(
            //       EZCache.shared.getUserProfile()?.employeeId ?? '',
            //       null,
            //       data?.items.first?.organizationId ?? '',
            //     );
            //     if (response?.code == ErrorCodes.success) {
            //       if (context.mounted) {
            //         unawaited(
            //           context.router.replace(
            //             HomeRoute(loginModel: response),
            //           ),
            //         );
            //       }
            //     }
            //   });

            //   // unawaited(
            //   //   context.router.replace(
            //   //       HomeRoute(loginModel: loginModel,
            //   //        )
            //   //    // '${Routes.inventory}/${data?.items.first?.organizationId}',
            //   //   ),
            //   // );
            // }
          }
        }
        if (state.status == HrOrganizationStatus.failure) {
          unawaited(
            ApiErrorDialog.show(
              ApiErrorParams(context, Utils.getData(state.data)),
            ),
          );
        }
      },
      builder: (final context, final state) {
        if (state.status == HrOrganizationStatus.success) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(top: 12, left: 16, right: 16),
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 6, left: 16),
                  child: Column(
                    children: [
                      ...List.generate(data?.items.length ?? 0, (final i) {
                        final item = data?.items[i];
                        return InkWell(
                          onTap: () async {
                            if (widget.toNextPage == ToNextPage.HR.name) {
                              context.router.pushNamed(
                                '${Routes.staffEvaluationPeriods}/${item?.organizationId}',
                              );
                            }
                            if (widget.toNextPage ==
                                ToNextPage.PRODUCT_CONFIRM.name) {
                              unawaited(
                                context.router.pushNamed(
                                  '${Routes.productConfirm}/${item?.organizationId}',
                                ),
                              );
                            }
                          },
                          child: Row(
                            children: <Widget>[
                              DecoratedBox(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    width: .35,
                                    color: Theme.of(context).dividerColor,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(3.0),
                                  child: ClipOval(
                                    child: EzCachedNetworkImage(
                                      fit: BoxFit.contain,
                                      imageUrl: item?.organizationUrl,
                                      width: 40,
                                      height: 40,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: i != (data?.items.length ?? 0) - 1
                                          ? BorderSide(
                                              width: .35,
                                              color: Theme.of(
                                                context,
                                              ).hintColor,
                                            )
                                          : BorderSide.none,
                                    ),
                                  ),

                                  child: SizedBox(
                                    height: 58,
                                    child: Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              item?.organizationName ?? '',
                                            ),
                                          ),
                                          EZResources.image(
                                            ImageParams(
                                              name: AppIcons.icArrowNext,
                                              size: const ImageSize.square(20),
                                              color: Theme.of(
                                                context,
                                              ).disabledColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ),
          );
        }

        return const Center(child: LoadingWidget());
      },
    );
  }
}
