// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';

// Project imports:
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/widgets.dart';
import 'custom_indicator_tabbar.dart';
import 'tag_by_room_tab.dart';

class TagByResultImageListBody extends StatefulWidget {
  const TagByResultImageListBody({
    super.key,
    required this.tags,
    required this.rooms,
    required this.tabController,
  });
  final ValueNotifierList<ComboTag> tags;
  final List<CustomerGetRoomListItem> rooms;
  final TabController tabController;
  @override
  State<TagByResultImageListBody> createState() =>
      _TagByResultImageListBodyState();
}

class _TagByResultImageListBodyState extends State<TagByResultImageListBody>
    with TickerProviderStateMixin {
  final ValueNotifierList<ImageByComboTag?> images = ValueNotifierList([]);
  final ValueNotifierList<ComboTag> comboTagSelected = ValueNotifierList([]);
  final Map<String, ValueNotifierList<ImageByComboTag?>> mapImageByRoom = {};
  @override
  void initState() {
    super.initState();
    for (final room in widget.rooms) {
      mapImageByRoom[room.roomCode ?? ''] = ValueNotifierList([]);
    }
    // Khởi tạo TabController với số lượng tabs bằng số lượng rooms
    // Đảm bảo có ít nhất 1 tab
    if (widget.tags.value.isNotEmpty) {
      final index = widget.rooms.indexWhere(
        (final room) => room.roomCode == widget.tags.value.first.itemGroupId,
      );
      if (index != -1) {
        widget.tabController.animateTo(index);
      }
    }
    widget.tabController.addListener(() {
      isEndList.value = false;
      isLoadingMore.value = false;
    });
  }

  final ValueNotifier<bool> isLoadingMore = ValueNotifier(false);
  final ValueNotifier<bool> isEndList = ValueNotifier(false);
  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<TagImageBloc, TagImageState>(
      listener: (final context, final state) {
        if (state.status == TagImageStatus.imageByComboTagSuccess) {
          final List<ImageByComboTag> data = state.data;
          images.setValue(data);
        }
        if (state.status == TagImageStatus.loadingGetMore) {
          isLoadingMore.value = true;
        }
        if (state.status == TagImageStatus.getMoreSuccess) {
          final List<ImageByComboTag> data = state.data;
          if (data.isEmpty) {
            isEndList.value = true;
            isLoadingMore.value = false;
          } else {
            mapImageByRoom[state.roomCode ?? '']?.addLastList(data);
            isLoadingMore.value = false;
          }
        }
        if (state.status == TagImageStatus.refreshSuccess) {
          final List<ImageByComboTag> data = state.data;
          mapImageByRoom[state.roomCode ?? '']?.setValue(
            data..sort((final a, final b) {
  if (a.createdDate == null && b.createdDate == null) {
    return 0;
  }
  if (a.createdDate == null) {
    return 1;
  }
  if (b.createdDate == null) {
    return -1;
  }
  
  try {
    final dateA = DateTime.parse(
      a.createdDate!.split('/').reversed.join('-'),
    );
    final dateB = DateTime.parse(
      b.createdDate!.split('/').reversed.join('-'),
    );
    return dateB.compareTo(dateA);
  } catch (e) {
    return 0;
  }
})
            );
          isLoadingMore.value = false;
          isEndList.value = false;
        }
        if (state.status == TagImageStatus.deleteTagSuccess) {
          final idxImage = state.idxImageByDate;
          final imageId = state.idImageByDate;
          final imageByDate =
              mapImageByRoom[state.roomCode ?? '']?.value[idxImage ?? 0];

          final indexWhereImage = imageByDate?.images.indexWhere(
            (final e) => e?.id == imageId,
          );
          if (indexWhereImage != -1) {
            imageByDate?.images.removeAt(indexWhereImage ?? 0);
            mapImageByRoom[state.roomCode ?? '']?.updateValuebyIndex(
              idxImage ?? 0,
              imageByDate,
            );
          }
          isLoadingMore.value = false;
          isEndList.value = false;
          EzToast.showWidgetToast(
            context,
            body: DecoratedBox(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: .8),
                borderRadius: BorderRadius.circular(12),
              ),
              child: SizedBox(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 12.0,
                  ),
                  child: Row(
                    spacing: 8,
                    children: [
                      DecoratedBox(
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(2.0),
                          child: Icon(
                            Icons.check,
                            size: 24,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      Text(
                        context.l10n.messageDeletedTag,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }
      },
      buildWhen: (final previous, final current) {
        return current.status != TagImageStatus.loadingGetMore &&
            current.status != TagImageStatus.getMoreSuccess &&
            current.status != TagImageStatus.loadingRefreshRoom &&
            current.status != TagImageStatus.refreshSuccess &&
            current.status != TagImageStatus.deleteImageSuccess &&
            current.status != TagImageStatus.deleteTagSuccess;
      },
      builder: (final context, final state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: TabBar(
                tabAlignment: TabAlignment.start,
                controller: widget.tabController,
                isScrollable: true,
                indicatorColor: Theme.of(context).primaryColor,
                indicatorWeight: 0,
                indicator: CustomTabIndicator(
                  indicatorHeight: 2,
                  color: Theme.of(context).primaryColor,
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Theme.of(
                  context,
                ).hintColor.withValues(alpha: .8),
                labelStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                unselectedLabelStyle: Theme.of(context).textTheme.labelLarge
                    ?.copyWith(fontSize: 16, fontWeight: FontWeight.w500),
                dividerColor: Colors.transparent,
                tabs: widget.rooms.isNotEmpty
                    ? widget.rooms
                          .map(
                            (final room) =>
                                Tab(height: 32, text: room.roomName ?? 'Room'),
                          )
                          .toList()
                    : [const Tab(text: 'No Rooms')],
              ),
            ),
            const Divider(thickness: .9),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: _buildTabView(),
              ),
            ),
          ],
        );
      },
    );
  }

  TabBarView _buildTabView() {
    return TabBarView(
      controller: widget.tabController,
      children: widget.rooms.isNotEmpty
          ? widget.rooms
                .map(
                  (final room) => ValueListenableBuilder(
                    valueListenable: images,
                    builder: (final context, final vImage, final child) {
                      final imageByRoom = vImage
                          .map((final e) {
                            if (e?.images == null) {
                              return null;
                            }

                            final filteredImages = e!.images.where((
                              final image,
                            ) {
                              return image?.tags?.any(
                                    (final tag) =>
                                        tag.itemGroupId == room.roomCode,
                                  ) ??
                                  false;
                            }).toList();

                            if (filteredImages.isNotEmpty) {
                              return ImageByComboTag(
                                createdDate: e.createdDate,
                                images: filteredImages,
                              );
                            }
                            return null;
                          })
                          .where((final e) => e != null)
                          .toList()
                          ..sort((final a, final b) {
  if (a?.createdDate == null && b?.createdDate == null) {
    return 0;
  }
  if (a?.createdDate == null) {
    return 1;
  }
  if (b?.createdDate == null) {
    return -1;
  }
  
  try {
    final dateA = DateTime.parse(
      a!.createdDate!.split('/').reversed.join('-'),
    );
    final dateB = DateTime.parse(
      b!.createdDate!.split('/').reversed.join('-'),
    );
    return dateB.compareTo(dateA);
  } catch (e) {
    return 0;
  }
});
                      mapImageByRoom[room.roomCode ?? '']?.setValue(
                        imageByRoom,
                      );
                      return ValueListenableBuilder(
                        valueListenable: widget.tags,
                        builder: (final context, final vTags, final child) {
                          final tagByRoom = vTags
                              .where(
                                (final tagRoom) =>
                                    tagRoom.itemGroupId == room.roomCode,
                              )
                              .toList();
                          return TagByRoomTab(
                            isLoadingMore: isLoadingMore,
                            isEndList: isEndList,
                            imageByRoom: mapImageByRoom[room.roomCode ?? ''],
                            tagByRoom: tagByRoom,
                            tags: widget.tags,
                            roomCode: room.roomCode ?? '',
                            roomName: context.l10n.room(room.roomName ?? ''),
                          );
                        },
                      );
                    },
                  ),
                )
                .toList()
          : [const Center(child: Text('No rooms available'))],
    );
  }
}
