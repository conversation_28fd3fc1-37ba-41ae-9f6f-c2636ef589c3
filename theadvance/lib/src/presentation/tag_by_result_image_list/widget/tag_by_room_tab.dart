// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/params/image_by_combo_delete_params.dart';
import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../core/params/story_write_info_request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../story_list/widgets/story_image.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/value_notifier_list.dart';
import '../view/preview_image_page.dart';

class TagByRoomTab extends StatefulWidget {
  const TagByRoomTab({
    super.key,
    required this.imageByRoom,
    required this.tagByRoom,
    required this.tags,
    required this.roomCode,
    required this.isLoadingMore,
    required this.isEndList,
    required this.roomName,
  });
  final String roomCode;
  final String roomName;
  final ValueNotifierList<ImageByComboTag?>? imageByRoom;
  final List<ComboTag> tagByRoom;
  final ValueNotifierList<ComboTag> tags;
  final ValueNotifier<bool> isLoadingMore;
  final ValueNotifier<bool> isEndList;

  @override
  State<TagByRoomTab> createState() => _TagByRoomTabState();
}

class _TagByRoomTabState extends State<TagByRoomTab> {
  late ScrollController _scrollController;
  int page = 1;
  @override
  void initState() {
    _scrollController = ScrollController()
      ..addListener(() {
        final maxScroll = _scrollController.position.maxScrollExtent;
        final currentScroll = _scrollController.offset;
        if (maxScroll > 500) {
          _lazyStoryAll(maxScroll, currentScroll);
        }
      });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _lazyStoryAll(final double maxScroll, final double currentScroll) {
    if (!widget.isEndList.value) {
      if (!widget.isLoadingMore.value) {
        if (currentScroll >= (maxScroll * .9)) {
          page++;
          context.read<TagImageBloc>().add(
            ImageByComboTagGetMore(
              ImageByComboTagQueryParams(
                itemGroupIds: widget.roomCode,
                tagIds: widget.tagByRoom
                    .map((final tag) => tag.tagId)
                    .join(','),
                pageNumber: page,
              ),
              widget.roomCode,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.isEndList,
      builder: (final context, final vEndList, final child) {
        return ValueListenableBuilder(
          valueListenable: widget.isLoadingMore,
          builder: (final context, final vLoadingMore, final child) {
            return Column(
              spacing: 4,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.tagByRoom.isNotEmpty &&
                    widget.tagByRoom.first.tagId != null)
                  _buildTagChip(widget.tagByRoom, context),
                if ((widget.tagByRoom.isNotEmpty &&
                        widget.tagByRoom.first.tagId == null) ||
                    widget.tagByRoom.isEmpty)
                  const SizedBox(height: 24),
                ValueListenableBuilder<List<ImageByComboTag?>?>(
                  valueListenable: widget.imageByRoom ?? ValueNotifierList([]),
                  builder: (final context, final vImageByRoom, final child) {
                    return Expanded(
                      child: RefreshIndicator.adaptive(
                        //  scrollController: _scrollController,
                        onRefresh: () async {
                          page = 1;
                          context.read<TagImageBloc>().add(
                            ImageByComboTagRefreshRoom(
                              ImageByComboTagQueryParams(
                                itemGroupIds: widget.roomCode,
                                tagIds:
                                    widget.tagByRoom.isNotEmpty &&
                                        widget.tagByRoom.firstOrNull?.tagId !=
                                            null
                                    ? widget.tagByRoom
                                          .map((final tag) => tag.tagId)
                                          .join(',')
                                    : null,
                                pageNumber: page,
                              ),
                              widget.roomCode,
                            ),
                          );
                        },
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          child: Column(
                            children: [
                              ...List.generate(vImageByRoom?.length ?? 0, (
                                final iImage,
                              ) {
                                final image = vImageByRoom?[iImage];
                                return Column(
                                  children: [
                                    _buildImageByDate(context, image, iImage),
                                  ],
                                );
                              }),

                              if (vLoadingMore)
                                MediaQuery.removePadding(
                                  context: context,
                                  removeTop: true,
                                  child: GridView.count(
                                    crossAxisCount: 3,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    mainAxisSpacing: 4,
                                    crossAxisSpacing: 4,
                                    children: [
                                      ...List.generate(3, (final i) {
                                        return const Center(
                                          child: LoadingWidget(),
                                        );
                                      }),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Column _buildImageByDate(
    final BuildContext context,
    final ImageByComboTag? image,
    final int idxImageByDate,
  ) {
    final images = image?.images.toList();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //  Text(image?.createdDate??''),
        MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: GridView.count(
            crossAxisCount: 3,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: 4,
            crossAxisSpacing: 4,
            children: [
              ...List.generate(images?.length ?? 0, (final i) {
                final imageItem = images?[i];
                return Stack(
                  fit: StackFit.passthrough,
                  children: [
                    StoryImage(
                      originalWidth: 1,
                      originalHeight: 1,
                      tags: imageItem?.imageUrl ?? '',
                      imageUrl: '${imageItem?.imageUrl}',
                    ),
                    GestureDetector(
                      onTap: () {
                        PreviewImagePage.images = images ?? [];
                        handleMedia(images, imageItem?.imageUrl ?? '');
                        pushStoryImageDetail(
                          context,
                          imageItem,
                          i: i,
                          idxImageByDate: idxImageByDate,
                        );
                      },
                      child: _buildOverlay(Attachment()),
                    ),
                  ],
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Padding _buildTagChip(
    final List<ComboTag> tagByRoom,
    final BuildContext context,
  ) {
    page = 1;
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 16, bottom: 24),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          ...List.generate(
            tagByRoom.where((final e) => e.tagId != null).length,
            (final i) {
              final tag = tagByRoom
                  .where((final e) => e.tagId != null)
                  .toList()[i];
              return GestureDetector(
                onTap: () {
                  widget.tagByRoom.remove(tag);
                  page = 1;
                  context.read<TagImageBloc>().add(
                    ImageByComboTagRefreshRoom(
                      ImageByComboTagQueryParams(
                        itemGroupIds: tag.itemGroupId,
                        tagIds:
                            widget.tagByRoom.isNotEmpty &&
                                widget.tagByRoom.firstOrNull?.tagId != null
                            ? widget.tagByRoom
                                  .map((final tag) => tag.tagId)
                                  .join(',')
                            : null,
                        pageNumber: page,
                      ),
                      tag.itemGroupId ?? '',
                    ),
                  );
                },
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      width: .35,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 4,
                      horizontal: 8,
                    ),
                    child: Row(
                      spacing: 4,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          tag.tagName ?? '',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        Icon(
                          Icons.cancel,
                          color: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          if (tagByRoom.where((final e) => e.tagId != null).length > 1)
            _buildDeleteAll(tagByRoom, context),
        ],
      ),
    );
  }

  GestureDetector _buildDeleteAll(
    final List<ComboTag> tagByRoom,
    final BuildContext context,
  ) {
    return GestureDetector(
      onTap: () {
        // ignore: prefer_foreach
        for (final ComboTag element in tagByRoom) {
          widget.tags.remove(element);
        }
      },
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black45, width: .35),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
          child: Row(
            spacing: 4,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.l10n.deleteAll,
                style: const TextStyle(
                  color: Colors.black45,
                  fontWeight: FontWeight.w400,
                ),
              ),
              EZResources.image(
                ImageParams(
                  name: AppIcons.icTrash,
                  color: Colors.black54,
                  size: const ImageSize.square(24),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  AspectRatio _buildOverlay(final Attachment? item) {
    return AspectRatio(
      aspectRatio:
          ((item?.width ?? 1) == 0 ? 1 : (item?.width ?? 1)) /
          ((item?.height ?? 1) == 0 ? 1 : (item?.height ?? 1)),
      child: Container(color: Colors.transparent),
    );
  }

  void handleMedia(
    final List<ImageByComboTagImages?>? items,
    final String tag,
  ) {
    final safeImageList = items
        ?.map((final e) => e ?? ImageByComboTagImages(tags: []))
        .toList();
    final listWidgetAttachment = safeImageList?.map((final eWidget) {
      return StoryImage(
        originalWidth: 1,
        originalHeight: 1,
        tags: tag,
        imageUrl: '${eWidget.imageUrl}',
        fit: BoxFit.contain,
        disableGestures: null,
        initialScale: PhotoViewComputedScale.contained,
        minScale: PhotoViewComputedScale.contained,
        maxScale: PhotoViewComputedScale.contained * 1.1,
      );
    }).toList();
    PreviewImagePage.listMedia.setValue(listWidgetAttachment);
  }

  Future<void> pushStoryImageDetail(
    final BuildContext context,
    final ImageByComboTagImages? item, {
    required final int i,
    required final int idxImageByDate,
  }) async {
    context.router
        .push(
          PreviewImageRoute(
            initIndex: i,
            roomName: widget.roomName,
            isShowAction: true,
            isShowbottom: true,
          ),
        )
        .then((final val) {
          if (val != null && val is TagByComboTagAction) {
            if (context.mounted) {
              if (val == TagByComboTagAction.remove) {
                context.read<TagImageBloc>().add(
                  ImageByComboTagDeleteImage(
                    ImageByComboTagDeleteParams(
                      id: item?.imageId,
                      deletedBy: EZCache.shared.getUserProfile()?.employeeId,
                      deletedName: EZCache.shared.getUserProfile()?.name,
                    ),
                    widget.roomCode,
                  ),
                );
              } else if (val == TagByComboTagAction.removeTag) {
                context.read<TagImageBloc>().add(
                  ImageByComboTagRemoveTag(
                    ImageByComboTagDeleteParams(
                      id: item?.id,
                      deletedBy: EZCache.shared.getUserProfile()?.employeeId,
                      deletedName: EZCache.shared.getUserProfile()?.name,
                    ),
                    widget.roomCode,
                    idxImageByDate,
                    item?.id ?? '',
                  ),
                );
              }
            }
          }
        });
  }
}
